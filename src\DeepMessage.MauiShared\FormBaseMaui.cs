﻿using DeepMessage.Framework.Core;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.ComponentModel;
using System.Windows.Input;
#if ANDROID
using Microsoft.Maui.Platform; // Add this namespace for ToPlatform extension method  
#endif

namespace DeepMessage.MauiShared
{
    public abstract class FormBaseMaui<TFormModel, TFormViewModel, TKey, TFormDataService> : ContentPage
         where TFormModel : class, new()
         where TFormViewModel : class, INotifyPropertyChanged, new()
         where TFormDataService : IFormDataService<TFormModel, TKey>
    {
        private TKey? _recordId;
        protected ILogger<TFormDataService> Logger { get; }
        private TFormViewModel _selectedItem = new TFormViewModel();

        public TFormViewModel SelectedItem
        {
            get => _selectedItem;
            set
            {
                _selectedItem = value;
                if (_selectedItem != null)
                {
                    _selectedItem.PropertyChanged += SelectedItem_PropertyChanged;
                }
                OnPropertyChanged();
            }
        }

        private bool _isWorking;
        public bool IsWorking
        {
            get => _isWorking;
            set
            {
                if (_isWorking != value)
                {
                    _isWorking = value;
                    OnPropertyChanged();
                }
            }
        }

        public IServiceScopeFactory ScopeFactory { get; }

        protected FormBaseMaui(IServiceScopeFactory scopeFactory, TKey? recordId = default)
        {
            ScopeFactory = scopeFactory ?? throw new ArgumentNullException(nameof(scopeFactory));

            using var scope = scopeFactory.CreateScope();
            Logger = scope.ServiceProvider.GetRequiredService<ILogger<TFormDataService>>();

            if (!EqualityComparer<TKey>.Default.Equals(recordId, default))
                _recordId = recordId;

            PropertyChanged += FormBase_PropertyChanged;
        }

        private void FormBase_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(SelectedItem) && SelectedItem != null)
            {
                OnSelectedItemCreated(SelectedItem);
            }
        }
        public virtual Color TitleBarColor { get; } = Color.FromArgb("#303030");



        void SetStatusBarColor(Color color)
        {
#if ANDROID
   var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;  

   if (window != null)  
   {  
       window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);  
       window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);  
       window.SetStatusBarColor(color.ToPlatform()); // Ensure the namespace is included  
   }  
#endif
        }

        protected virtual void OnSelectedItemCreated(TFormViewModel model) { }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            SetStatusBarColor(TitleBarColor);
            MainThread.BeginInvokeOnMainThread(() => IsWorking = true);

            Task.Run(async () =>
            {
                try
                {
                    using var scope = ScopeFactory.CreateScope();
                    await LoadSelectLists(scope).ConfigureAwait(false);

                    if (IsNewRecord(_recordId))
                    {
                        var newItem = await CreateSelectedItem().ConfigureAwait(false);
                        MainThread.BeginInvokeOnMainThread(() => SelectedItem = newItem);
                    }
                    else
                    {
                        var crudService = scope.ServiceProvider.GetRequiredService<TFormDataService>();
                        var formModel = await crudService.GetItemByIdAsync(_recordId).ConfigureAwait(false)
                            ?? throw new InvalidDataException("Selected Item is null after service.GetItemByIdAsync");

                        var viewModel = await ConvertBusinessModelToViewModel(formModel).ConfigureAwait(false)
                            ?? throw new InvalidDataException("Item not found");

                        MainThread.BeginInvokeOnMainThread(() => SelectedItem = viewModel);
                    }
                     
                    MainThread.BeginInvokeOnMainThread(() => IsWorking = false);
                }
                catch (Exception ex)
                {
                    LogAndDisplayError(ex);
                }
            });
        }

        private static bool IsNewRecord(TKey? id) =>
            id == null || id.ToString() is "0" or "" or "00000000-0000-0000-0000-000000000000";

        protected virtual Task<TFormViewModel?> ConvertBusinessModelToViewModel(TFormModel formModel)
        {
            return formModel == null
                ? throw new ArgumentNullException(nameof(formModel), "formModel is null while cloning")
                : Task.FromResult(formModel.Clone<TFormViewModel?>());
        }

        public virtual void SelectedItem_PropertyChanged(object? sender, PropertyChangedEventArgs e) { }

        protected virtual Task LoadSelectLists(IServiceScope scope) => Task.CompletedTask;

        protected virtual Task<TFormViewModel> CreateSelectedItem() => Task.FromResult(new TFormViewModel());

        public virtual Task BeforeSaveAsync() => Task.CompletedTask;

        public virtual Task OnAfterSaveAsync(TKey key) => Task.CompletedTask;

        protected virtual TFormModel ConvertViewModelToBusinessModel(TFormViewModel? formViewModel)
        {
            return formViewModel == null
                ? throw new ArgumentNullException(nameof(formViewModel), "ViewModel cannot be null")
                : formViewModel.Clone<TFormModel>();
        }

        private ICommand _saveCommand;
        public ICommand SaveCommand
        {
            get
            {
                if (_saveCommand == null)
                {
                    _saveCommand = new Command(async () =>
                    {
                        await HandleFormSubmit(null!, null!);
                    });
                }
                return _saveCommand;
            }
        }

        public async Task HandleFormSubmit(object sender, EventArgs e)
        {
            try
            {
                Logger.LogInformation("Form submitted");
                await MainThread.InvokeOnMainThreadAsync(() => IsWorking = true);
                await Task.Delay(100);
                using var scope = ScopeFactory.CreateScope();
                var crudService = scope.ServiceProvider.GetRequiredService<TFormDataService>();

                if (SelectedItem is IValidateable validateable)
                {
                    validateable.Validate();
                }

                var businessModel = ConvertViewModelToBusinessModel(SelectedItem)
                    ?? throw new InvalidDataException("Business model is null before calling SaveAsync");

                await BeforeSaveAsync();
                var id = await crudService.SaveAsync(businessModel);
                await OnAfterSaveAsync(id);
            }
            catch (Exception ex)
            {
                LogAndDisplayError(ex);
            }
            finally
            {
                await MainThread.InvokeOnMainThreadAsync(() => IsWorking = false);
            }
        }

        private void LogAndDisplayError(Exception ex)
        {
            Logger.LogError(ex, "An error occurred");
            //Crashes.TrackError(ex);
            MainThread.BeginInvokeOnMainThread(() => DisplayAlert("Error", ex.Message, "Ok"));
        }

    }

    public static class ObjectExtensions
    {
        public static T Clone<T>(this object model)
        {
            if (model == null)
                throw new ArgumentNullException(nameof(model), "Cannot clone a null object");

            var json = JsonConvert.SerializeObject(model);
            return JsonConvert.DeserializeObject<T>(json)
                ?? throw new InvalidOperationException("Object cloning failed");
        }
    }
}
﻿using DeepMessage.Server.DataServices.Data;
using DeepMessage.Server.DataServices.Helpers;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using FirebaseAdmin.Messaging;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Hybrid;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using System.Collections.Concurrent;
using System.Security.Claims;
using System.Text.Json;
using System.Threading.Channels;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace DeepMessage.Server.DataServices;

public class DeepChatHub : Hub
{
    private static ConcurrentDictionary<string, string> connectionIdCache = new ConcurrentDictionary<string, string>();
    private readonly IServiceScopeFactory scopeFactory;

    public ILogger<DeepChatHub> Logger { get; }

    public DeepChatHub(ILogger<DeepChatHub> logger, IServiceScopeFactory scopeFactory)
    {
        Logger = logger;
        this.scopeFactory = scopeFactory;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null)
        {
            Logger.LogWarning("Unauthorized connection attempt from {ConnectionId}", Context.ConnectionId);
            await Clients.Client(Context.ConnectionId).SendAsync("Logout");
            Context.Abort();
            return;
        }

        Logger.LogInformation("User connected - ConnectionId: {ConnectionId}, UserId: {UserId}, UserName: {UserName}",
            Context.ConnectionId, userId, Context.User.Identity?.Name);

        // Handle multiple connections for same user
        if (connectionIdCache.TryGetValue(userId, out var existingConnectionId))
        {
            Logger.LogInformation("User {UserId} already has connection {ExistingConnectionId}, replacing with {NewConnectionId}",
                userId, existingConnectionId, Context.ConnectionId);
        }

        connectionIdCache.AddOrUpdate(userId, Context.ConnectionId, (key, oldValue) => Context.ConnectionId);

        await base.OnConnectedAsync();

        try
        {
            await SendPendingMessages(userId);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending pending messages to user {UserId}", userId);
        }
    }

    private async Task SendPendingMessages(string userId)
    {
        var scope = scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var pendingMessages = (from m in context.Messages
                               from recipients in context.MessageRecipients.Where(x => x.MessageId == m.Id)
                               from user in context.Users.Where(x => x.Id == recipients.RecipientId)
                               where recipients.DeliveryStatus <= DeliveryStatus.SentToEndUserViaSignalR
                               && recipients.RecipientId == userId
                               orderby m.Id
                               select new ChatMessagesSyncFormBusinessObject
                               {
                                   Id = m.Id,
                                   MessageRecepientId = recipients.Id,
                                   MessageRecepientUserName = user.UserName,
                                   ConversationId = m.ConversationId,
                                   SenderId = m.SenderId,
                                   CreatedAt = m.CreatedAt,
                                   DeletedAt = m.DeletedAt,
                                   DisappearAfter = m.DisappearAfter,
                                   DisappearAt = m.DisappearAt,
                                   EditedAt = m.EditedAt,
                                   IsDeleted = m.IsDeleted,
                                   IsEdited = m.IsEdited,
                                   IsEphemeral = m.IsEphemeral,
                                   MessageRecipients = m.Recipients.Select(r => new MessageRecipientSyncFormBusinessObject
                                   {
                                       Id = r.Id,
                                       MessageId = r.MessageId,
                                       RecipientId = r.RecipientId,
                                       EncryptedContent = r.EncryptedContent,
                                       DeliveryStatus = r.DeliveryStatus,
                                       DeliveryStatusTime = r.DeliveryStatusTime
                                   }).ToList(),
                               }).ToList();
        pendingMessages = pendingMessages.DistinctBy(x => x.Id).ToList();
        foreach (var message in pendingMessages)
        {
            await SendMessageAsync(userId, message.MessageRecepientUserName, message.Id, message.PlainContent,
                 JsonSerializer.Serialize(message), SignalRMethod.OnNewFeed);
        }

        var utcNow = DateTime.UtcNow;

        var messageIds = pendingMessages.Select(x => x.Id).ToList();
        await context.Messages.Where(x => messageIds.Contains(x.Id) && x.DeliveryStatus < DeliveryStatus.SentToEndUserViaSignalR)
                      .ExecuteUpdateAsync(x => x
                      .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaSignalR)
                      .SetProperty(p => p.DeliveryStatusTime, utcNow));

        var messageRecipients = pendingMessages.Select(x => x.MessageRecepientId).ToList();
        await context.MessageRecipients.Where(x => messageRecipients.Contains(x.Id))
            .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaSignalR)
                        .SetProperty(p => p.DeliveryStatusTime, utcNow));
    }

    public async Task AcknowledgeMessage(string messageId, DeliveryStatus deliveryStatus, DateTime timeStamp)
    {
        var userId = Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (string.IsNullOrEmpty(userId))
        {
            Logger.LogWarning("Received Ack without valid user context.");
            return;
        }
        Logger.LogWarning("Received Ack  for {0} from {1} - {2}.", messageId, userId, deliveryStatus);
        var scope = scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var messageDispatcher = scope.ServiceProvider.GetRequiredService<MessageDispatcher>();
        await context.MessageRecipients.Where(x => x.MessageId == messageId && x.RecipientId == userId)
              .ExecuteUpdateAsync(x => x.SetProperty(y => y.DeliveryStatus, deliveryStatus)
              .SetProperty(y=>y.DeliveryStatusTime, timeStamp));

        messageDispatcher.DispatchMessageUpdate(new ChatMessageUpdate
        {
            Id = messageId,
            DeliveryStatusTime = timeStamp,
            SenderId = userId,
            DeliveryStatus = deliveryStatus
        });
    }


    public async Task<bool> SendMessageAsync(string userId, string? userName, string messageId, string? content, string messageJson, SignalRMethod signalRMethod)
    {
        try
        {
            if (string.IsNullOrEmpty(userId))
            {
                Logger.LogWarning("Cannot send message: userId is null or empty");
                return false;
            }

            var connectionId = connectionIdCache.ContainsKey(userId) ? connectionIdCache[userId] : string.Empty;

            if (string.IsNullOrEmpty(connectionId))
            {
                Logger.LogDebug("User {UserName} (ID: {UserId}) is offline - message {MessageId} will be queued", userName, userId, messageId);
                return false;
            }

            if (Clients == null)
            {
                Logger.LogError("SignalR Clients is null - cannot send message {MessageId}", messageId);
                return false;
            }

            var client = Clients.Client(connectionId);
            if (client == null)
            {
                Logger.LogWarning("SignalR client not found for user {UserName} (ID: {UserId}), connection {ConnectionId}", userName, userId, connectionId);
                // Remove stale connection from cache
                connectionIdCache.TryRemove(userId, out _);
                return false;
            }

            await client.SendAsync(signalRMethod.ToString(), userId, messageJson);
            Logger.LogDebug("Message {MessageId} sent to {UserName} (ID: {UserId}) via {Method} on connection {ConnectionId}",
                messageId, userName, userId, signalRMethod, connectionId);
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error sending message {MessageId} to user {UserName} (ID: {UserId}) via SignalR", messageId, userName, userId);
            // Remove potentially stale connection
            connectionIdCache.TryRemove(userId, out _);
            return false;
        }
    }

    public override async Task OnDisconnectedAsync(Exception? e)
    {
        var userId = Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (!string.IsNullOrEmpty(userId))
        {
            // Only remove if this is the current connection for the user
            if (connectionIdCache.TryGetValue(userId, out var cachedConnectionId) &&
                cachedConnectionId == Context.ConnectionId)
            {
                connectionIdCache.TryRemove(userId, out _);
                Logger.LogInformation("User disconnected - ConnectionId: {ConnectionId}, UserId: {UserId}, UserName: {UserName}, Reason: {Reason}",
                    Context.ConnectionId, userId, Context.User.Identity?.Name, e?.Message ?? "Normal disconnect");
            }
            else
            {
                Logger.LogDebug("Connection {ConnectionId} for user {UserId} was already replaced, not removing from cache",
                    Context.ConnectionId, userId);
            }
        }
        else
        {
            Logger.LogWarning("Disconnected connection {ConnectionId} had no valid user ID", Context.ConnectionId);
        }

        await base.OnDisconnectedAsync(e);
    }

}
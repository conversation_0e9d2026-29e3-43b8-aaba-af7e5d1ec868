﻿using DeepMessage.Framework.Core;
using DeepMessage.ServiceContracts.Enums;
namespace DeepMessage.ServiceContracts.Features.Conversation;
public class ChatMessagesListingBusinessObject
{
    public string Id { get; set; } = null!;

    public string? Content { get; set; }
    public DateTime? Timestamp { get; set; }
    public bool IsIncoming { get; set; }

    public DeliveryStatus DeliveryStatus { get; set; }

    /// <summary>
    /// Media attachments for display in message bubbles
    /// </summary>
    public List<MessageAttachmentInfo>? Attachments { get; set; }
}

/// <summary>
/// Attachment information for message display
/// </summary>
public class MessageAttachmentInfo
{
    public string Id { get; set; } = null!;

    public string AttachmentType { get; set; } = null!;

    public string FileName { get; set; } = null!;

    public string? FileUrl { get; set; }

    public long? FileSizeBytes { get; set; }

    public string? MimeType { get; set; }

    /// <summary>
    /// Base64 encoded thumbnail for quick display
    /// </summary>
    public string? ThumbnailBase64 { get; set; }

    /// <summary>
    /// Upload progress for pending uploads (0-100)
    /// </summary>
    public int UploadProgress { get; set; } = 100;

    /// <summary>
    /// Whether the attachment is currently being uploaded
    /// </summary>
    public bool IsUploading { get; set; } = false;
}

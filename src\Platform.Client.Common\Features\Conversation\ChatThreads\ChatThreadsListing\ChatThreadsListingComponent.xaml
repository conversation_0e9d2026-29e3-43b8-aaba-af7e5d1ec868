﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatThreadsListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatThreadsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:chat="clr-namespace:Platform.Client.Services.Features.Conversation;assembly=Platform.Client.Services"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    Title="Chats"
    x:DataType="local:ChatThreadsListingView"
    BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                      Dark={StaticResource Gray800}}"
    Shell.BackgroundColor="{AppThemeBinding Light={StaticResource White},
                                            Dark={StaticResource Gray800}}"
    Shell.TitleColor="{AppThemeBinding Light={StaticResource Gray800},
                                       Dark={StaticResource White}}">

    <ContentPage.ToolbarItems>
        <ToolbarItem IconImageSource="search.svg" />
        <ToolbarItem Command="{Binding SyncDownItemsCommand}" IconImageSource="refresh.svg" />
        <ToolbarItem IconImageSource="menu.svg" />
    </ContentPage.ToolbarItems>
   

        <Grid
   
            RowDefinitions="Auto,4, *"
            VerticalOptions="Fill">

            <Border
                Margin="8,0,8,0"
                BackgroundColor="{AppThemeBinding Light={StaticResource Gray100},
                                                  Dark={StaticResource Gray700}}"
                Stroke="{AppThemeBinding Light={StaticResource Gray50},
                                         Dark={StaticResource Gray500}}">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="45" />
                </Border.StrokeShape>
                <Grid>
                    <Image
                        MaximumHeightRequest="32" Margin="8,0"  HorizontalOptions="End"
                        Source="search_dark.svg" />
                    <Entry
                        Grid.Column="0"
                        Margin="8,0"
                        FontSize="16" 
                        HorizontalOptions="Fill"
                        Placeholder="Type a message"
                        Text="{Binding SelectedItem.Content, Mode=TwoWay}" />


                </Grid>
            </Border>

        <Border VerticalOptions="Fill"  
     BackgroundColor="{AppThemeBinding Light={StaticResource Gray50},
                                       Dark=Black}" 
     Stroke="{AppThemeBinding Light=Gainsboro,
                              Dark={StaticResource Gray800}}"
                Grid.Row="2"
     StrokeThickness="0.5">
            <!--<Border.StrokeShape>
         <RoundRectangle CornerRadius="16,16,0,0" />
     </Border.StrokeShape>-->
            <CollectionView
                ItemsSource="{Binding Items}"
                ItemsUpdatingScrollMode="KeepLastItemInView">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="chat:ChatThreadsListingViewModel">
                        <Grid Padding="12" ColumnSpacing="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="50" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:ChatThreadsListingView}}, Path=BindingContext.MessageTappedCommand}" CommandParameter="{Binding .}" />
                            </Grid.GestureRecognizers>
                            <Border
                                Grid.Column="0"
                                BackgroundColor="Gray"
                                HeightRequest="50"
                                WidthRequest="50">
                                <Border.StrokeShape>
                                    <RoundRectangle CornerRadius="45" />
                                </Border.StrokeShape>
                                <Image
                                    Aspect="AspectFill"
                                    HeightRequest="50"
                                    Source="{Binding Avatar}"
                                    WidthRequest="50" />
                            </Border>
                            <StackLayout Grid.Column="1" Spacing="2">
                                <Grid>
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="18"
                                        Text="{Binding Name}"
                                        TextColor="{AppThemeBinding Light=Black,
                                                                    Dark=White}" />

                                    <Label
                                        FontSize="14"
                                        HorizontalOptions="End"
                                        Text="{Binding LastMessageTimeString}"
                                        TextColor="{AppThemeBinding Light=Gray,
                                                                    Dark=LightGray}" />

                                </Grid>
                                <Label
                                    FontSize="16"
                                    LineBreakMode="TailTruncation"
                                    MaxLines="1"
                                    Text="{Binding LastMessage}"
                                    TextColor="{AppThemeBinding Light=Gray,
                                                                Dark=LightGray}" />
                            </StackLayout>

                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
          </Border></Grid>
  
</local:ChatThreadsListingViewBase>

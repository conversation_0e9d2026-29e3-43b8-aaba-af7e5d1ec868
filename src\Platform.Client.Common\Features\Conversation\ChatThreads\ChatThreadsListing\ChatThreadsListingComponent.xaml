﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatThreadsListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatThreadsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    xmlns:chat="clr-namespace:Platform.Client.Services.Features.Conversation;assembly=Platform.Client.Services"
    Title="Chats"
    x:DataType="local:ChatThreadsListingView"
    Background="{StaticResource Gray700}"
    Shell.TitleColor="White">

    <ContentPage.ToolbarItems>
        <ToolbarItem IconImageSource="search.svg" />
        <ToolbarItem Command="{Binding SyncDownItemsCommand}" IconImageSource="refresh.svg" />
        <ToolbarItem IconImageSource="menu.svg" />
    </ContentPage.ToolbarItems>
    <Border
        BackgroundColor="{AppThemeBinding Light=WhiteSmoke,
                                          Dark=Black}"
        Stroke="{AppThemeBinding Light=Gainsboro,
                                 Dark={StaticResource Gray800}}"
        StrokeThickness="1">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        <CollectionView ItemsSource="{Binding Items}" ItemsUpdatingScrollMode="KeepLastItemInView">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="chat:ChatThreadsListingViewModel">
                    <Grid Padding="12" ColumnSpacing="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="50" />
                            <ColumnDefinition Width="*" />
                              <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:ChatThreadsListingView}}, Path=BindingContext.MessageTappedCommand}" CommandParameter="{Binding .}" />
                        </Grid.GestureRecognizers>
                        <Border
                            Grid.Column="0"
                            BackgroundColor="Gray"
                            HeightRequest="50"
                            WidthRequest="50">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="45" />
                            </Border.StrokeShape>
                            <Image
                                Aspect="AspectFill"
                                HeightRequest="50"
                                Source="{Binding Avatar}"
                                WidthRequest="50" />
                        </Border>
                        <StackLayout Grid.Column="1" Spacing="2">
                            <Grid>
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="18"
                                    Text="{Binding Name}"
                                    TextColor="{AppThemeBinding Light=Black,
                                                                Dark=White}" />

                                <Label
                                    FontSize="14"
                                    HorizontalOptions="End"
                                    Text="{Binding LastMessageTimeString}"
                                    TextColor="{AppThemeBinding Light=Gray,
                                                                Dark=LightGray}" />

                            </Grid>
                            <Label
                                FontSize="16"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Text="{Binding LastMessage}"
                                TextColor="{AppThemeBinding Light=Gray,
                                                            Dark=LightGray}" />
                        </StackLayout>

                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </Border>
</local:ChatThreadsListingViewBase>

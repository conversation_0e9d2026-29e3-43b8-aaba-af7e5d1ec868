﻿using DeepMessage.Framework.Core;
using DeepMessage.Framework.Enums;
using DeepMessage.Server.DataServices.Data;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using DeepMessage.Server.DataServices.Services;
namespace DeepMessage.Server.DataServices.Features.Conversation;
public class ChatMessageServerSideFormDataService : IChatMessageFormDataService
{

    private readonly AppDbContext _context;
    private readonly IHttpContextAccessor contextAccessor;
    private readonly IServerEncryptionService encryptionService;

    public ChatMessageServerSideFormDataService(AppDbContext context, IHttpContextAccessor contextAccessor,
        IServerEncryptionService encryptionService)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
        this.encryptionService = encryptionService;
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
        ArgumentException.ThrowIfNullOrEmpty(userId);

        // Input validation
        if (string.IsNullOrWhiteSpace(formBusinessObject.Content))
        {
            throw new ArgumentException("Message content cannot be empty");
        }

        if (formBusinessObject.Content.Length > 4000) // Reasonable message length limit
        {
            throw new ArgumentException("Message content exceeds maximum length of 4000 characters");
        }

        // Sanitize content (basic HTML encoding to prevent XSS)
        var sanitizedContent = System.Net.WebUtility.HtmlEncode(formBusinessObject.Content.Trim());

        // Verify user is participant in the conversation
        var isParticipant = await _context.ConversationParticipants
            .AnyAsync(cp => cp.ConversationId == formBusinessObject.ConversationId && cp.UserId == userId);

        if (!isParticipant)
        {
            throw new UnauthorizedAccessException("User is not a participant in this conversation");
        }

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Create message with NO plaintext content for E2E encryption
            var message = new Message()
            {
                Id = Guid.CreateVersion7().ToString(),
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = DateTime.UtcNow,
                PlainContent = null, // ✅ NO PLAINTEXT STORAGE - E2E ENCRYPTION
                SenderId = userId,
                DeliveryStatus = ServiceContracts.Enums.DeliveryStatus.Pending
            };

            _context.Messages.Add(message);

            // Get conversation participants with their public keys
            var participantsWithKeys = await (from cp in _context.ConversationParticipants
                                             join u in _context.Users on cp.UserId equals u.Id
                                             where cp.ConversationId == message.ConversationId
                                             select new
                                             {
                                                 UserId = cp.UserId,
                                                 PublicKey = u.Pub1,
                                                 IsSender = cp.UserId == userId
                                             }).ToListAsync();

            // Create encrypted MessageRecipient records for each participant
            foreach (var participant in participantsWithKeys)
            {
                if (string.IsNullOrEmpty(participant.PublicKey))
                {
                    throw new InvalidOperationException($"Public key not found for participant {participant.UserId}");
                }

                var encryptedContent = encryptionService.EncryptWithRSAPublicKey(sanitizedContent, participant.PublicKey);

                var messageRecipient = new MessageRecipient()
                {
                    Id = Guid.CreateVersion7().ToString().ToLower(),
                    MessageId = message.Id,
                    RecipientId = participant.UserId,
                    UserDeviceId = participant.UserId, // For now, using UserId as device ID
                    EncryptedContent = encryptedContent, // ✅ PROPERLY ENCRYPTED CONTENT
                    DeliveryStatus = ServiceContracts.Enums.DeliveryStatus.SentToMessageServer,
                    DeliveryStatusTime = DateTime.UtcNow,
                    IsRead = participant.IsSender // Mark as read for sender
                };

                _context.MessageRecipients.Add(messageRecipient);
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            return message.Id;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    [SystemClaim(SystemClaimType.SystemDefault)]
    public Task<ChatMessageFormBusinessObject> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}

<?xml version="1.0" encoding="utf-8" ?>
<local:FakeCaptchaScreenBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.FakeCaptchaScreen"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    Title="Activate your account"
    x:DataType="local:FakeCaptchaScreen"
    BackgroundColor="#********"
    IsBusy="True">
    <Grid Padding="16" RowDefinitions="1*, Auto, 2*">
        <Border
            x:Name="MainBorder"
            Grid.Row="1"
            Padding="16"
            BackgroundColor="White"
            StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="8" />
            </Border.StrokeShape>

            <ScrollView>
                <VerticalStackLayout>

                    <!--  Header Section  -->
                    <VerticalStackLayout Margin="0,16,0,0" Spacing="8">
                        <!--  Conditional display based on registration status  -->
                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="20"
                            HorizontalTextAlignment="Center"
                            Text="{Binding HeaderMessage}"
                            TextColor="{StaticResource Secondary700}" />

                        <!--  Activation status or instructions  -->
                        <Label
                            x:Name="lblSubHeader"
                            FontFamily="Poppins"
                            FontSize="14"
                            HorizontalTextAlignment="Center"
                            Opacity="0.8"
                            Text="{Binding SubHeaderMessage}"
                            TextColor="#6B7280" />
                    </VerticalStackLayout>

                    <!--  Reset Registration Option (shown when user is registered)  -->
                    <HorizontalStackLayout HorizontalOptions="Center" IsVisible="{Binding IsUserRegistered}">
                        <Label
                            Grid.Column="0"
                            FontSize="12"
                            Text="{Binding ObfuscatedUsername, StringFormat='Activated for {0}'}"
                            TextColor="{StaticResource Secondary600}"
                            VerticalOptions="Center" />
                        <Button
                            Grid.Column="1"
                            BackgroundColor="Transparent"
                            Command="{Binding ResetRegistrationCommand}"
                            FontSize="12"
                            IsEnabled="True"
                            Text="Reset"
                            TextColor="{StaticResource Blue600}" />
                    </HorizontalStackLayout>

                    <!--  Captcha Section (shown when user is registered)  -->
                    <VerticalStackLayout Padding="16" IsVisible="{Binding IsUserRegistered}">
                        <!--  Captcha Image Placeholder  -->
                        <Border
                            BackgroundColor="#F3F4F6"
                            HeightRequest="64"
                            Stroke="#D1D5DB"
                            StrokeThickness="2"
                            WidthRequest="192">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="4" />
                            </Border.StrokeShape>
                            <Image
                                HorizontalOptions="Center"
                                Source="{Binding CaptchaImage}"
                                VerticalOptions="Center" />
                        </Border>

                        <!--  Captcha Controls  -->
                        <HorizontalStackLayout HorizontalOptions="Center" Spacing="16">
                            <Button
                                BackgroundColor="Transparent"
                                Command="{Binding RefreshCaptchaCommand}"
                                FontSize="12"
                                TextColor="{StaticResource Blue600}">
                                <Button.ImageSource>
                                    <FileImageSource File="refresh_icon.svg" />
                                </Button.ImageSource>
                                <Button.Text>Refresh</Button.Text>
                            </Button>
                            <Button
                                BackgroundColor="Transparent"
                                Command="{Binding AudioCaptchaCommand}"
                                FontSize="12"
                                TextColor="{StaticResource Blue600}">
                                <Button.ImageSource>
                                    <FileImageSource File="volume_icon.svg" />
                                </Button.ImageSource>
                                <Button.Text>Audio</Button.Text>
                            </Button>
                        </HorizontalStackLayout>
                    </VerticalStackLayout>

                    <Label Margin="0,16" Text="You can obtain purchase code from brieflynews.com/purchase or get a refferal code from a friend." />

                    <!--  Enhanced Error Display  -->
                    <Border
                        BackgroundColor="#FEF2F2"
                        IsVisible="{Binding HasError}"
                        Stroke="#FECACA"
                        StrokeThickness="1">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="8" />
                        </Border.StrokeShape>
                        <Grid Padding="12" ColumnDefinitions="Auto,*">
                            <Image
                                Grid.Column="0"
                                Margin="0,2,8,0"
                                HeightRequest="20"
                                Source="error_icon.svg"
                                VerticalOptions="Start"
                                WidthRequest="20" />
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Verification failed"
                                    TextColor="#DC2626" />
                                <Label
                                    FontSize="12"
                                    Text="{Binding Error}"
                                    TextColor="#991B1B" />
                            </VerticalStackLayout>
                        </Grid>
                    </Border>

                    <!--  Auth Code Form  -->
                    <VerticalStackLayout Spacing="16">

                        <!--  Auth Code Field  -->
                        <VerticalStackLayout Margin="0,8,0,0" Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="MulishExtraBold"
                                FontSize="14"
                                Text="{Binding AuthCodeLabel}"
                                TextColor="{StaticResource Gray600}" />
                            <Grid  >
                                <Image
                                    HorizontalOptions="End"
                                    Source="lock_keyhole_solid.svg"
                                    WidthRequest="24" />
                                <Entry
                                    x:Name="entryAuthCode"
                                    Grid.Column="1"
                                    BackgroundColor="Transparent"
                                    FontSize="16"
                                    IsPassword="True"
                                    Placeholder="{Binding AuthCodePlaceholder}"
                                    PlaceholderColor="#9CA3AF"
                                    Text="{Binding SelectedItem.PassKey}" />
                            </Grid>
                        </VerticalStackLayout>

                        <!--  Enhanced Submit Button  -->
                        <Button 
                            Command="{Binding SaveCommand}"
                            HeightRequest="44" Margin="16,8" 
                            Text="Next" >

                        </Button>

                        <!--  Loading Indicator  -->
                        <ActivityIndicator
                            IsRunning="{Binding IsWorking}"
                            IsVisible="True"
                            Color="#004f98" />

                        <!--  Back to Search Link  -->
                        <HorizontalStackLayout HorizontalOptions="Center" Spacing="5">
                            <Label
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Already activated an account?"
                                TextColor="#6B7280" />
                            <Label
                                x:Name="ClickHereLabel"
                                FontAttributes="Bold"
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Click here"
                                TextColor="#004f98">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding GoToLoginCommand}" />
                                </Label.GestureRecognizers>
                            </Label>
                        </HorizontalStackLayout>
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </ScrollView>
        </Border>
    </Grid>
</local:FakeCaptchaScreenBase>
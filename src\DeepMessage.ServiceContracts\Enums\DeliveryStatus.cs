﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DeepMessage.ServiceContracts.Enums
{
    public enum DeliveryStatus : byte
    {
        Pending = 0,
        QueuedToUpSync = 1,
        DeliveryFailed = 8,
        SentToMessageServer = 16,
        SentToEndUserViaPushNotification = 32,
        SentToEndUserViaSignalR = 40,
        DeliveredToEndUser = 48,
        ReadByEndUser = 64,

    }
}

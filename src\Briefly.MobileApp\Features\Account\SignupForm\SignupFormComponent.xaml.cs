﻿using ModelFury.Briefly.MobileApp.Features.Account;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Account;
using Microsoft.Extensions.DependencyInjection;
using MobileApp.MauiShared;
using System.Security.Claims;
using System.Text.Json;
using System.Windows.Input;
using Java.Time;
using Platform.Framework.Core;
using System.Collections.ObjectModel;

namespace ModelFury.Briefly.MobileApp.Features.Account;

public class SignupFormViewBase : FormBaseMaui<SignupFormBusinessObject, SignupFormViewModel, string, ISignupFormDataService>
{
    public SignupFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

[QueryProperty(nameof(AuthCode), "AuthCode")]
public partial class SignupFormView : SignupFormViewBase
{
    private string? authCode;
    
    public string? AuthCode
    {
        get => authCode;
        set => authCode = value;
    }

    // UI State Properties
    private bool displayNameFieldFocused;
    public bool DisplayNameFieldFocused
    {
        get => displayNameFieldFocused;
        set
        {
            displayNameFieldFocused = value;
            OnPropertyChanged();
        }
    }

    private bool usernameFieldFocused;
    public bool UsernameFieldFocused
    {
        get => usernameFieldFocused;
        set
        {
            usernameFieldFocused = value;
            OnPropertyChanged();
        }
    }

    private bool passwordFieldFocused;
    public bool PasswordFieldFocused
    {
        get => passwordFieldFocused;
        set
        {
            passwordFieldFocused = value;
            OnPropertyChanged();
        }
    }

    private bool hasError;
    public bool HasError
    {
        get => hasError;
        set
        {
            hasError = value;
            OnPropertyChanged();
        }
    }

    private ObservableCollection<string> avatarOptions;
    public ObservableCollection<string> AvatarOptions
    {
        get => avatarOptions;
        set
        {
            avatarOptions = value;
            OnPropertyChanged();
        }
    }

    public ICommand TogglePasswordVisibilityCommand { get; }
    public ICommand GoToSigninCommand { get; }

    public override Color TitleBarColor => Color.FromArgb("#004f98");
     
    public SignupFormView(IServiceScopeFactory scopeFactory) : base(scopeFactory, null!)
    {
        InitializeComponent();
        BindingContext = this;

        TogglePasswordVisibilityCommand = new Command(TogglePasswordVisibility);
        GoToSigninCommand = new Command(async () => await GoToSignin());

        // Initialize avatar options
        InitializeAvatarOptions();
    }

    private void InitializeAvatarOptions()
    {
        AvatarOptions = new ObservableCollection<string>();
        for (int i = 1; i <= 12; i++)
        {
            AvatarOptions.Add($"/avatars/{i}.png");
        }
    }
     
    protected override Task<SignupFormViewModel> CreateSelectedItem()
    {
        return Task.FromResult(new SignupFormViewModel()
        {
            DeviceString = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
            ShowPassword = false,
            AvatarData = string.Empty,
            DisplayName = string.Empty,
            NickName = string.Empty,
            PassKey = string.Empty
        });
    }
     

    private void TogglePasswordVisibility()
    {
        if (SelectedItem != null)
        {
            SelectedItem.ShowPassword = !SelectedItem.ShowPassword;
            OnPropertyChanged(nameof(SelectedItem));
        }
    }

    private async Task GoToSignin()
    {
        await Shell.Current.GoToAsync("//signin");
        await Navigation.PopToRootAsync();
    }

    // Focus event handlers
    private void OnDisplayNameFocused(object sender, FocusEventArgs e)
    {
        DisplayNameFieldFocused = true;
    }

    private void OnDisplayNameUnfocused(object sender, FocusEventArgs e)
    {
        DisplayNameFieldFocused = false;
    }

    private void OnUsernameFocused(object sender, FocusEventArgs e)
    {
        UsernameFieldFocused = true;
    }

    private void OnUsernameUnfocused(object sender, FocusEventArgs e)
    {
        UsernameFieldFocused = false;
    }

    private void OnPasswordFocused(object sender, FocusEventArgs e)
    {
        PasswordFieldFocused = true;
    }

    private void OnPasswordUnfocused(object sender, FocusEventArgs e)
    {
        PasswordFieldFocused = false;
    }

    public override async Task OnAfterSaveAsync(string key)
    {
        using var scope = ScopeFactory.CreateScope();
        var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

        var authClaims = JsonSerializer.Deserialize<AuthorizationClaimsModel>(key);
        await storageService.SetValue(authClaims.Token, "auth_token");
        await storageService.SetValue(authClaims.RefreshToken, "refresh_token");
        await storageService.SetValue(authClaims.UserId, ClaimTypes.NameIdentifier);
        await storageService.SetValue(authClaims.Username, ClaimTypes.Name);

        // Sync user profile data including avatar for new users
        var profileSyncService = scope.ServiceProvider.GetRequiredService<Platform.Client.Services.Features.Account.IProfileSyncService>();
        await profileSyncService.SyncUserProfileAsync(authClaims.UserId, authClaims.Username);

        // Notify authentication state provider
        var authStateProvider = scope.ServiceProvider.GetRequiredService<CustomAuthStateProvider>();
        authStateProvider.NotifyUserAuthentication(authClaims.UserId, authClaims.Username);

        await Shell.Current.GoToAsync("//messages");
        await Navigation.PopToRootAsync();
    }
}

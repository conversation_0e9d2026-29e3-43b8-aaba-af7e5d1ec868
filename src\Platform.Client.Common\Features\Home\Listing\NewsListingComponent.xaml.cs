﻿using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Home;
using Platform.Client.Services.Features.Home;
namespace Platform.Client.Common.Features.Home;
public class NewsListingViewBase : ListingBaseMaui<NewsListingViewModel,NewsListingBusinessObject,NewsFilterViewModel,NewsFilterBusinessObject, INewsListingDataService>
{
	public NewsListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
	{
	}
}


public partial class NewsListingView : NewsListingViewBase
{
	public NewsListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
	{
	InitializeComponent();
	}
}



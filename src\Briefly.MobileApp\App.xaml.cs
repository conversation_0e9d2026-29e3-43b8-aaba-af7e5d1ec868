﻿using Android.Views;
using CommunityToolkit.Mvvm.Messaging;
using DeepMessage.MauiApp.Services; 
using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Platform;
using MobileApp.MauiShared;
using Platform.Framework.Core;

namespace ModelFury.Briefly.MobileApp
{
    public partial class App : Application
    {
        private readonly IServiceScopeFactory serviceScopeFactory;
        private readonly ILogger<App> logger;

        public App(IServiceScopeFactory serviceScopeFactory, ILogger<App> logger)
        {
            InitializeComponent();
#if ANDROID
            //DependencyService.Register<DeepMessage.MauiApp.Platforms.Android.ForegroundServiceManager>();
#endif

            this.serviceScopeFactory = serviceScopeFactory;
            this.logger = logger;
//            Microsoft.Maui.Handlers.EntryHandler.Mapper.AppendToMapping("MyCustomization", (handler, view) =>
//            {
//#if ANDROID
//                handler.PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(Colors.Transparent.ToAndroid());
//#endif
//            });

//            Microsoft.Maui.Handlers.EditorHandler.Mapper.AppendToMapping("MyCustomization", (handler, view) =>
//            {
//#if ANDROID
//                handler.PlatformView.BackgroundTintList = Android.Content.Res.ColorStateList.ValueOf(Colors.Transparent.ToAndroid());
//#endif
           
//            });

        }
    

        protected override Microsoft.Maui.Controls.Window CreateWindow(IActivationState? activationState)
        {
            return new Microsoft.Maui.Controls.Window(new AppShell(serviceScopeFactory));
        }

        protected override void OnStart()
        {
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
             logger.LogError("Unhandled exception: {Exception}", args.ExceptionObject);
            };

            WeakReferenceMessenger.Default.Register<string>(this, async (r, m) =>
            {
                if (m == "Logout")
                {
                    await Shell.Current.GoToAsync("//signin");
                }
            });

         
            StartSyncing();
            base.OnStart();
             
        }

        protected override void OnResume()
        {
            StartSyncing();
            base.OnResume();
        }

        private void StartSyncing()
        {
            try
            {
                var scope = serviceScopeFactory.CreateScope();
                scope.ServiceProvider.GetRequiredService<ChatSyncUpService>().Start();
                scope.ServiceProvider.GetRequiredService<SignalRClientService>().Start(MauiProgram.ChatHubUrl);

                //DependencyService.Get<IForegroundServiceManager>()?.StartService();
            }
            catch (Exception ex)
            {
                logger.LogError(ex.ToString());
            }
        }

        protected override void OnSleep()
        {
            try
            {
                var scope = serviceScopeFactory.CreateScope();
                scope.ServiceProvider.GetRequiredService<ChatSyncUpService>().Stop();
            }
            catch (Exception ex)
            {
                logger.LogError(ex.ToString());
            }
            base.OnSleep();
        }
    }

}
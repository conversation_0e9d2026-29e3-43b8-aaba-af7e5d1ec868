using DeepMessage.Client.Common.Data;
using DeepMessage.ServiceContracts.Enums;
using DeepMessage.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace Platform.Client.Services.Services
{
    public interface IOfflineSyncService
    {
        Task StartAsync();
        Task StopAsync();
        Task QueueMessageForSync(string messageId);
        Task HandleNetworkConnectivity(bool isConnected);
        Task<bool> IsOnlineAsync();
        Task SyncPendingMessagesAsync();
    }

    public class OfflineSyncService : IOfflineSyncService
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ILogger<OfflineSyncService> _logger;
        private readonly ConcurrentQueue<string> _syncQueue;
        private readonly Timer _syncTimer;
        private readonly Timer _connectivityTimer;
        private bool _isOnline;
        private bool _isRunning;
        private readonly SemaphoreSlim _syncSemaphore;

        public OfflineSyncService(IServiceScopeFactory scopeFactory, ILogger<OfflineSyncService> logger)
        {
            _scopeFactory = scopeFactory;
            _logger = logger;
            _syncQueue = new ConcurrentQueue<string>();
            _syncSemaphore = new SemaphoreSlim(1, 1);
            _isOnline = true; // Assume online initially

            // Sync every 30 seconds
            _syncTimer = new Timer(async _ => await SyncPendingMessagesAsync(), null, 
                TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

            // Check connectivity every 10 seconds
            _connectivityTimer = new Timer(async _ => await CheckConnectivityAsync(), null,
                TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
        }

        public Task StartAsync()
        {
            _isRunning = true;
            _logger.LogInformation("Offline sync service started");
            return Task.CompletedTask;
        }

        public Task StopAsync()
        {
            _isRunning = false;
            _syncTimer?.Dispose();
            _connectivityTimer?.Dispose();
            _logger.LogInformation("Offline sync service stopped");
            return Task.CompletedTask;
        }

        public Task QueueMessageForSync(string messageId)
        {
            if (string.IsNullOrEmpty(messageId))
                return Task.CompletedTask;

            _syncQueue.Enqueue(messageId);
            _logger.LogDebug("Message {MessageId} queued for sync", messageId);

            // Trigger immediate sync if online
            if (_isOnline)
            {
                _ = Task.Run(async () => await SyncPendingMessagesAsync());
            }

            return Task.CompletedTask;
        }

        public Task HandleNetworkConnectivity(bool isConnected)
        {
            var wasOnline = _isOnline;
            _isOnline = isConnected;

            _logger.LogInformation("Network connectivity changed: {Status}", isConnected ? "Online" : "Offline");

            // If we just came back online, trigger sync
            if (isConnected && !wasOnline)
            {
                _ = Task.Run(async () => await SyncPendingMessagesAsync());
            }

            return Task.CompletedTask;
        }

        public Task<bool> IsOnlineAsync()
        {
            return Task.FromResult(_isOnline);
        }

        public async Task SyncPendingMessagesAsync()
        {
            if (!_isRunning || !_isOnline)
                return;

            await _syncSemaphore.WaitAsync();
            try
            {
                using var scope = _scopeFactory.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                // Get messages that need syncing
                var pendingMessages = await context.Messages
                    .Where(m => m.DeliveryStatus == DeliveryStatus.Pending || 
                               m.DeliveryStatus == DeliveryStatus.QueuedToUpSync)
                    .OrderBy(m => m.CreatedAt)
                    .Take(50) // Batch size
                    .ToListAsync();

                if (pendingMessages.Count == 0)
                {
                    _logger.LogDebug("No pending messages to sync");
                    return;
                }

                _logger.LogInformation("Syncing {Count} pending messages", pendingMessages.Count);

                var syncService = scope.ServiceProvider.GetRequiredService<IChatMessagesSyncFormDataService>();
                var successCount = 0;
                var failureCount = 0;

                foreach (var message in pendingMessages)
                {
                    try
                    {
                        var syncObject = new ChatMessagesSyncFormBusinessObject
                        {
                            Id = message.Id,
                            ConversationId = message.ConversationId,
                            SenderId = message.SenderId,
                            PlainContent = message.PlainContent,
                            CreatedAt = message.CreatedAt,
                            IsDeleted = message.IsDeleted,
                            IsEdited = message.IsEdited,
                            IsEphemeral = message.IsEphemeral,
                            DeletedAt = message.DeletedAt,
                            EditedAt = message.EditedAt,
                            DisappearAfter = message.DisappearAfter,
                            DisappearAt = message.DisappearAt
                        };

                        await syncService.SaveAsync(syncObject);

                        // Update status to synced
                        message.DeliveryStatus = DeliveryStatus.SentToMessageServer;
                        successCount++;

                        _logger.LogDebug("Successfully synced message {MessageId}", message.Id);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to sync message {MessageId}", message.Id);
                        
                        // Mark as failed after multiple attempts
                        if (message.DeliveryStatus == DeliveryStatus.QueuedToUpSync)
                        {
                            message.DeliveryStatus = DeliveryStatus.DeliveryFailed;
                        }
                        else
                        {
                            message.DeliveryStatus = DeliveryStatus.QueuedToUpSync;
                        }
                        
                        failureCount++;
                    }
                }

                await context.SaveChangesAsync();

                _logger.LogInformation("Sync completed: {SuccessCount} successful, {FailureCount} failed", 
                    successCount, failureCount);

                // Process queued messages
                await ProcessQueuedMessages();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during sync operation");
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        private async Task ProcessQueuedMessages()
        {
            var processedCount = 0;
            while (_syncQueue.TryDequeue(out var messageId) && processedCount < 10) // Limit batch size
            {
                try
                {
                    using var scope = _scopeFactory.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                    var message = await context.Messages.FirstOrDefaultAsync(m => m.Id == messageId);
                    if (message != null && message.DeliveryStatus == DeliveryStatus.Pending)
                    {
                        message.DeliveryStatus = DeliveryStatus.QueuedToUpSync;
                        await context.SaveChangesAsync();
                        processedCount++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing queued message {MessageId}", messageId);
                }
            }

            if (processedCount > 0)
            {
                _logger.LogDebug("Processed {Count} queued messages", processedCount);
            }
        }

        private async Task CheckConnectivityAsync()
        {
            try
            {
                // Simple connectivity check - try to reach the server
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(5);
                
                var response = await httpClient.GetAsync("https://www.google.com");
                var isConnected = response.IsSuccessStatusCode;

                if (isConnected != _isOnline)
                {
                    await HandleNetworkConnectivity(isConnected);
                }
            }
            catch
            {
                // If we can't reach the internet, assume offline
                if (_isOnline)
                {
                    await HandleNetworkConnectivity(false);
                }
            }
        }

        public void Dispose()
        {
            _syncTimer?.Dispose();
            _connectivityTimer?.Dispose();
            _syncSemaphore?.Dispose();
        }
    }
}

using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using MobileApp.MauiShared;
using Platform.Client.Services.Features.Friends;
using Platform.Framework.Core;
using System.Security.Claims;
using System.Windows.Input;
using System.ComponentModel;

namespace Platform.Client.Common.Features.Friends;

public class FriendsListingViewBase : ListingBaseMaui<FriendsListingViewModel, FriendsListingBusinessObject,
                                        FriendsFilterViewModel, FriendsFilterBusinessObject, IFriendsListingDataService>
{
    public FriendsListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }

    #region Computed Properties for UI State Management

    /// <summary>
    /// Indicates if there's an error to display
    /// </summary>
    public bool HasError => !string.IsNullOrEmpty(Error);

    /// <summary>
    /// Indicates if there's a success message to display
    /// </summary>
    public bool HasSuccessMessage => !string.IsNullOrEmpty(SuccessMessage);

    /// <summary>
    /// Indicates if search results info should be displayed
    /// </summary>
    public bool HasSearchResults => !string.IsNullOrEmpty(FilterViewModel?.SearchText) && Items.Count > 0;

    /// <summary>
    /// Indicates if the list is empty (no items to display)
    /// </summary>
    public bool IsEmpty => Items.Count == 0 && !IsWorking;

    /// <summary>
    /// Total records count (alias for TotalRows from base class)
    /// </summary>
    public int TotalRecords => TotalRows;

    #endregion

    #region Additional Properties

    private string? _successMessage;
    /// <summary>
    /// Success message to display to user
    /// </summary>
    public string? SuccessMessage
    {
        get => _successMessage;
        set
        {
            _successMessage = value;
            NotifyPropertyChanged();
            NotifyPropertyChanged(nameof(HasSuccessMessage));
        }
    }

    private bool _isSyncing;
    /// <summary>
    /// Indicates if sync operation is in progress
    /// </summary>
    public bool IsSyncing
    {
        get => _isSyncing;
        set
        {
            _isSyncing = value;
            NotifyPropertyChanged();
        }
    }

    #endregion

    #region Override Base Properties to Trigger UI Updates

    /// <summary>
    /// Override Error property to trigger UI state updates
    /// </summary>
    public new string? Error
    {
        get => base.Error;
        set
        {
            if (base.Error != value)
            {
                base.Error = value;
                NotifyPropertyChanged();
                NotifyPropertyChanged(nameof(HasError));
            }
        }
    }

    /// <summary>
    /// Override IsWorking property to trigger UI state updates
    /// </summary>
    public new bool IsWorking
    {
        get => base.IsWorking;
        set
        {
            if (base.IsWorking != value)
            {
                base.IsWorking = value;
                NotifyPropertyChanged();
                NotifyPropertyChanged(nameof(IsEmpty));
            }
        }
    }

    #endregion
}


public partial class FriendsListingView : FriendsListingViewBase
{
    public FriendsListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        InitializeCommands();
        SetupSearchHandling();
        BindingContext = this;
    }

    /// <summary>
    /// Sets up automatic search handling when search text changes
    /// </summary>
    private void SetupSearchHandling()
    {
        FilterViewModel.PropertyChanged += async (sender, e) =>
        {
            if (e.PropertyName == nameof(FilterViewModel.SearchText))
            {
                await LoadItems();
                NotifyPropertyChanged(nameof(HasSearchResults));
            }
        };
    }

    #region Command Initialization

    private void InitializeCommands()
    {
        // Initialize all commands required by the XAML
        SyncFriendsCommand = new Command(async () => await SyncFriends(), () => !IsWorking);
        AddFriendCommand = new Command(async () => await AddFriend());
        ClearSearchCommand = new Command(async () => await ClearSearch());
        RefreshCommand = new Command(async () => await RefreshItems());
        StartChatCommand = new Command<FriendsListingViewModel>(async (friend) => await StartChat(friend));
        EditFriendCommand = new Command<FriendsListingViewModel>(async (friend) => await EditFriend(friend));

        // Legacy command for backward compatibility
        MessageTappedCommand = StartChatCommand;
    }

    #endregion

    #region Commands

    public ICommand SyncFriendsCommand { get; private set; }
    public ICommand AddFriendCommand { get; private set; }
    public ICommand ClearSearchCommand { get; private set; }
    public ICommand RefreshCommand { get; private set; }
    public ICommand StartChatCommand { get; private set; }
    public ICommand EditFriendCommand { get; private set; }
    public ICommand MessageTappedCommand { get; private set; } // Legacy support

    #endregion

    #region Command Implementations

    /// <summary>
    /// Synchronizes friends from server
    /// </summary>
    private async Task SyncFriends()
    {
        try
        {
            IsSyncing = true;
            Error = string.Empty;
            SuccessMessage = string.Empty;

            using var scope = ScopeFactory.CreateScope();
            var friendsClientService = scope.ServiceProvider.GetRequiredKeyedService<IFriendsListingDataService>("client");
            var pagedItems = await friendsClientService.GetPaginatedItems(new FriendsFilterBusinessObject() { RowsPerPage = 50 });

            if (pagedItems?.Items != null)
            {
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var userId = await localStorage.GetValue(ClaimTypes.NameIdentifier);
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

                foreach (var item in pagedItems.Items)
                {
                    context.Friendships.Add(new Friendship()
                    {
                        Id = item.Id,
                        UserId = userId,
                        FriendId = item.FriendId,
                        Name = item.Name,
                        AvatarData = item.AvatarData,
                        Pub1 = item.Pub1, // Friend's RSA public key for message encryption
                    });
                }
                await context.SaveChangesAsync();
                await LoadItems();
                SuccessMessage = $"Successfully synced {pagedItems.Items.Count} friends";
            }
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
        finally
        {
            IsSyncing = false;
        }
    }

    /// <summary>
    /// Opens the add friend form
    /// </summary>
    private async Task AddFriend()
    {
        try
        {
            await Navigation.PushModalAsync(new FriendFormView(ScopeFactory, string.Empty));
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
    }

    /// <summary>
    /// Clears the search text and refreshes the list
    /// </summary>
    private async Task ClearSearch()
    {
        try
        {
            FilterViewModel.SearchText = string.Empty;
            await LoadItems();
            NotifyPropertyChanged(nameof(HasSearchResults));
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
    }

    /// <summary>
    /// Refreshes the friends list
    /// </summary>
    private async Task RefreshItems()
    {
        try
        {
            Error = string.Empty;
            SuccessMessage = string.Empty;
            await LoadItems();
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
    }

    /// <summary>
    /// Starts a chat with the selected friend
    /// </summary>
    private async Task StartChat(FriendsListingViewModel friend)
    {
        if (friend == null) return;

        try
        {
            var startChatService = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IStartChatFormDataService>();
            var conversationId = await startChatService.SaveAsync(new StartChatFormBusinessObject() { FriendId = friend.FriendId });

            // TODO: Navigate to chat view when implemented
            // await Navigation.PushAsync(new ChatFormView(conversationId));
            SuccessMessage = $"Chat started with {friend.Name}";
        }
        catch (Exception ex)
        {
            Error = ex.InnerException?.Message ?? ex.Message;
        }
    }

    /// <summary>
    /// Opens the edit friend form
    /// </summary>
    private async Task EditFriend(FriendsListingViewModel friend)
    {
        if (friend == null) return;

        try
        {
            await Navigation.PushModalAsync(new FriendFormView(ScopeFactory, friend.Id));
        }
        catch (Exception ex)
        {
            Error = ex.Message;
        }
    }

    #endregion

    #region Overrides and Event Handlers

    bool retry = true;
    protected override async Task ItemsLoaded(IFriendsListingDataService service)
    {
        // Auto-sync on first load if no items exist
        if (Items.Count == 0 && retry)
        {
            retry = false;
            await SyncFriends();
        }

        // Update UI state properties
        NotifyPropertyChanged(nameof(HasSearchResults));
        NotifyPropertyChanged(nameof(IsEmpty));
        NotifyPropertyChanged(nameof(TotalRecords));
    }

    /// <summary>
    /// Legacy toolbar item click handler
    /// </summary>
    private void ToolbarItem_Clicked(object sender, EventArgs e)
    {
        _ = AddFriend();
    }

    /// <summary>
    /// Override LoadItems to trigger UI state updates
    /// </summary>
    public override async Task LoadItems(bool showLoader = true)
    {
        await base.LoadItems(showLoader);

        // Trigger UI state property updates
        NotifyPropertyChanged(nameof(HasSearchResults));
        NotifyPropertyChanged(nameof(IsEmpty));
        NotifyPropertyChanged(nameof(TotalRecords));
    }

    /// <summary>
    /// Override UpdateItems to trigger UI state updates
    /// </summary>
    public override void UpdateItems(PagedDataList<FriendsListingBusinessObject> pagedItems, FriendsListingViewModel[] viewModelItems)
    {
        base.UpdateItems(pagedItems, viewModelItems);

        // Trigger UI state property updates
        MainThread.BeginInvokeOnMainThread(() =>
        {
            NotifyPropertyChanged(nameof(HasSearchResults));
            NotifyPropertyChanged(nameof(IsEmpty));
            NotifyPropertyChanged(nameof(TotalRecords));
        });
    }

    #endregion
}



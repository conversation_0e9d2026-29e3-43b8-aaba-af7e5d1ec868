﻿using DeepMessage.Client.Common.Data;
using DeepMessage.MauiShared;
using DeepMessage.ServiceContracts.Features.Conversation;
using DeepMessage.ServiceContracts.Features.Friends;
using MobileApp.MauiShared;
using Platform.Client.Services.Features.Friends;
using Platform.Framework.Core;
using System.Security.Claims;
using System.Windows.Input;
namespace Platform.Client.Common.Features.Friends;
public class FriendsListingViewBase : ListingBaseMaui<FriendsListingViewModel, FriendsListingBusinessObject,
                                        FriendsFilterViewModel, FriendsFilterBusinessObject, IFriendsListingDataService>
{
    public FriendsListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class FriendsListingView : FriendsListingViewBase
{
    public FriendsListingView(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
        InitializeComponent();
        MessageTappedCommand = new Command<FriendsListingViewModel>(async (p) =>
        {
            try
            {
                var startChatService = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IStartChatFormDataService>();
                var conversationId = await startChatService.SaveAsync(new StartChatFormBusinessObject() { FriendId = p.FriendId });

                throw new Exception();
                //await Navigation.PushAsync(new ChatFormView(conversationId));
            }
            catch (Exception ex)
            {
                Error = ex.InnerException?.Message;
            }
        });

        BindingContext = this;
    }

    bool retry = true;
    protected override async Task ItemsLoaded(IFriendsListingDataService service)
    {
        if (Items.Count == 0 && retry)
        {
            try
            {
                //lets try pulling from the server
                using var scope = ScopeFactory.CreateScope();
                var friendsClientService = scope.ServiceProvider.GetRequiredKeyedService<IFriendsListingDataService>("client");
                var pagedItems = await friendsClientService.GetPaginatedItems(new FriendsFilterBusinessObject() { RowsPerPage = 50 });
                ArgumentNullException.ThrowIfNull(pagedItems?.Items);
                var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var userId = await localStorage.GetValue(ClaimTypes.NameIdentifier);
                var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                foreach (var item in pagedItems.Items)
                {
                    context.Friendships.Add(new Friendship()
                    {
                        Id = item.Id,
                        UserId = userId,
                        FriendId = item.FriendId,
                        Name = item.Name,
                        AvatarData = item.AvatarData,
                        Pub1 = item.Pub1, // Friend's RSA public key for message encryption
                    });
                }
                await context.SaveChangesAsync();

                retry = false;
                await LoadItems();
            }
            catch (Exception ex)
            {
                Error = ex.Message;
            }
        }

    }

    private void ToolbarItem_Clicked(object sender, EventArgs e)
    {
        Navigation.PushModalAsync(new FriendFormView(ScopeFactory, string.Empty));
    }

    public ICommand MessageTappedCommand { get; set; }

}



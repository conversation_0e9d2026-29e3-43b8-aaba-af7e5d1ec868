<?xml version="1.0" encoding="utf-8" ?>
<local:FriendsListingViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    xmlns:friends="clr-namespace:Platform.Client.Services.Features.Friends;assembly=Platform.Client.Services"
    Title="Friends"
    x:DataType="local:FriendsListingViewBase"
    BackgroundColor="White"
    Shell.TitleColor="White">

    <Grid RowDefinitions="Auto,*">

        <!-- Header Section -->
        <VerticalStackLayout Grid.Row="0" BackgroundColor="White" Padding="16" Spacing="16">

            <!-- Title and Action Buttons -->
            <Grid ColumnDefinitions="*,Auto,Auto">
                <Label Grid.Column="0"
                       FontSize="24"
                       FontAttributes="Bold"
                       Text="Friends"
                       TextColor="#004f98"
                       VerticalOptions="Center" />

                <!-- Sync Button -->
                <Button Grid.Column="1"
                        BackgroundColor="#E5F3FF"
                        Command="{Binding SyncFriendsCommand}"
                        WidthRequest="40"
                        HeightRequest="40"
                        CornerRadius="20"
                        Margin="0,0,8,0"
                        IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}">
                    <Button.ImageSource>
                        <FileImageSource File="{Binding IsSyncing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='refresh_spin_icon.svg|refresh_icon.svg'}" />
                    </Button.ImageSource>
                </Button>

                <!-- Add Friend Button -->
                <Button Grid.Column="2"
                        BackgroundColor="#004f98"
                        Command="{Binding AddFriendCommand}"
                        WidthRequest="40"
                        HeightRequest="40"
                        CornerRadius="20">
                    <Button.ImageSource>
                        <FileImageSource File="plus_icon.svg" />
                    </Button.ImageSource>
                </Button>
            </Grid>

            <!-- Search Bar -->
            <Border BackgroundColor="#F9FAFB"
                    StrokeThickness="1"
                    Stroke="#E5E7EB"
                    HeightRequest="44">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="22" />
                </Border.StrokeShape>
                <Grid ColumnDefinitions="Auto,*,Auto" Margin="12,0">
                    <Image Grid.Column="0"
                           Source="search_icon.svg"
                           WidthRequest="16"
                           HeightRequest="16"
                           VerticalOptions="Center"
                           Margin="0,0,8,0" />
                    <Entry Grid.Column="1"
                           Placeholder="Search Friends..."
                           PlaceholderColor="#9CA3AF"
                           Text="{Binding FilterViewModel.SearchText}"
                           TextColor="#374151"
                           BackgroundColor="Transparent"
                           FontSize="16" />
                    <Button Grid.Column="2"
                            BackgroundColor="Transparent"
                            Command="{Binding ClearSearchCommand}"
                            IsVisible="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}}"
                            WidthRequest="32"
                            HeightRequest="32"
                            Padding="4">
                        <Button.ImageSource>
                            <FileImageSource File="close_icon.svg" />
                        </Button.ImageSource>
                    </Button>
                </Grid>
            </Border>
        </VerticalStackLayout>
        <!-- Content Section -->
        <ScrollView Grid.Row="1" BackgroundColor="White">
            <VerticalStackLayout Spacing="0">

                <!-- Success Message -->
                <Border IsVisible="{Binding HasSuccessMessage}"
                        BackgroundColor="#F0FDF4"
                        StrokeThickness="1"
                        Stroke="#BBF7D0"
                        Margin="16,0,16,8">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image Grid.Column="0"
                               Source="check_icon.svg"
                               WidthRequest="20"
                               HeightRequest="20"
                               VerticalOptions="Start"
                               Margin="0,2,8,0" />
                        <Label Grid.Column="1"
                               FontSize="14"
                               Text="{Binding SuccessMessage}"
                               TextColor="#166534" />
                    </Grid>
                </Border>

                <!-- Error Message -->
                <Border IsVisible="{Binding HasError}"
                        BackgroundColor="#FEF2F2"
                        StrokeThickness="1"
                        Stroke="#FECACA"
                        Margin="16,0,16,8">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <VerticalStackLayout Padding="12" Spacing="8">
                        <Grid ColumnDefinitions="Auto,*">
                            <Image Grid.Column="0"
                                   Source="error_icon.svg"
                                   WidthRequest="20"
                                   HeightRequest="20"
                                   VerticalOptions="Start"
                                   Margin="0,2,8,0" />
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label FontAttributes="Bold"
                                       FontSize="14"
                                       Text="Error loading friends"
                                       TextColor="#DC2626" />
                                <Label FontSize="14"
                                       Text="{Binding Error}"
                                       TextColor="#991B1B" />
                            </VerticalStackLayout>
                        </Grid>
                        <Button BackgroundColor="#DC2626"
                                Command="{Binding RefreshCommand}"
                                Text="Try Again"
                                TextColor="White"
                                FontSize="14"
                                FontAttributes="Bold"
                                HeightRequest="36"
                                HorizontalOptions="Start"
                                Padding="16,8" />
                    </VerticalStackLayout>
                </Border>

                <!-- Search Results Info -->
                <Border IsVisible="{Binding HasSearchResults}"
                        BackgroundColor="#E5F3FF"
                        StrokeThickness="1"
                        Stroke="#B3D9FF"
                        Margin="16,0,16,8">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="*,Auto">
                        <Label Grid.Column="0"
                               FontSize="14"
                               TextColor="#1E40AF">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span FontAttributes="Bold" Text="{Binding TotalRecords}" />
                                    <Span Text=" friends found for " />
                                    <Span FontAttributes="Bold" Text="{Binding FilterViewModel.SearchText, StringFormat='&quot;{0}&quot;'}" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Button Grid.Column="1"
                                BackgroundColor="Transparent"
                                Command="{Binding ClearSearchCommand}"
                                Text="Clear search"
                                TextColor="#1E40AF"
                                FontSize="14"
                                Padding="8,4" />
                    </Grid>
                </Border>

                <!-- Empty State -->
                <VerticalStackLayout IsVisible="{Binding IsEmpty}"
                                     Padding="16,64,16,16"
                                     Spacing="16"
                                     HorizontalOptions="Center">
                    <Image Source="users_icon.svg"
                           WidthRequest="64"
                           HeightRequest="64"
                           HorizontalOptions="Center" />
                    <Label FontSize="18"
                           FontAttributes="Bold"
                           Text="No friends found"
                           TextColor="#111827"
                           HorizontalTextAlignment="Center" />
                    <Label FontSize="14"
                           TextColor="#6B7280"
                           HorizontalTextAlignment="Center"
                           MaximumWidthRequest="320">
                        <Label.Text>
                            <Binding Path="FilterViewModel.SearchText">
                                <Binding.Converter>
                                    <StaticResource Key="StringToBoolConverter" />
                                </Binding.Converter>
                                <Binding.ConverterParameter>No friends match your search|You haven't added any friends yet. Start by adding your first friend!</Binding.ConverterParameter>
                            </Binding>
                        </Label.Text>
                    </Label>
                    <Button BackgroundColor="#004f98"
                            Command="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}, ConverterParameter='{Binding ClearSearchCommand}|{Binding AddFriendCommand}'}"
                            Text="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Clear Search|Add Your First Friend'}"
                            TextColor="White"
                            FontSize="14"
                            FontAttributes="Bold"
                            HeightRequest="44"
                            Padding="24,12" />
                </VerticalStackLayout>

                <!-- Friends List -->
                <CollectionView ItemsSource="{Binding Items}"
                                IsVisible="{Binding IsEmpty, Converter={StaticResource InverseBoolConverter}}"
                                BackgroundColor="Transparent">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="friends:FriendsListingViewModel">
                            <Grid Padding="16,12" ColumnDefinitions="Auto,*,Auto,Auto" BackgroundColor="White">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.StartChatCommand}" CommandParameter="{Binding .}" />
                                </Grid.GestureRecognizers>

                                <!-- Avatar with Status -->
                                <Grid Grid.Column="0" WidthRequest="48" HeightRequest="48">
                                    <Border BackgroundColor="#E5E7EB"
                                            WidthRequest="48"
                                            HeightRequest="48">
                                        <Border.StrokeShape>
                                            <RoundRectangle CornerRadius="24" />
                                        </Border.StrokeShape>
                                        <Image Source="{Binding AvatarData, Converter={StaticResource Base64ToImageConverter}}"
                                               IsVisible="{Binding AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                               Aspect="AspectFill" />
                                        <Label IsVisible="{Binding AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                               Text="{Binding Name, Converter={StaticResource InitialsConverter}}"
                                               FontSize="18"
                                               FontAttributes="Bold"
                                               TextColor="#004f98"
                                               HorizontalOptions="Center"
                                               VerticalOptions="Center" />
                                    </Border>
                                    <!-- Online Status -->
                                    <Ellipse Fill="#10B981"
                                             WidthRequest="14"
                                             HeightRequest="14"
                                             HorizontalOptions="End"
                                             VerticalOptions="End"
                                             Margin="0,0,-2,-2" />
                                </Grid>

                                <!-- Friend Info -->
                                <VerticalStackLayout Grid.Column="1"
                                                     Margin="12,0,8,0"
                                                     VerticalOptions="Center"
                                                     Spacing="2">
                                    <Label FontSize="16"
                                           FontAttributes="Bold"
                                           Text="{Binding Name}"
                                           TextColor="#111827"
                                           LineBreakMode="TailTruncation" />
                                    <Label FontSize="14"
                                           Text="{Binding TagLine}"
                                           TextColor="#6B7280"
                                           LineBreakMode="TailTruncation"
                                           IsVisible="{Binding TagLine, Converter={StaticResource StringToBoolConverter}}" />
                                </VerticalStackLayout>

                                <!-- Edit Button -->
                                <Button Grid.Column="2"
                                        BackgroundColor="#6B7280"
                                        Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.EditFriendCommand}"
                                        CommandParameter="{Binding .}"
                                        WidthRequest="36"
                                        HeightRequest="36"
                                        CornerRadius="18"
                                        Margin="0,0,8,0">
                                    <Button.ImageSource>
                                        <FileImageSource File="edit_icon.svg" />
                                    </Button.ImageSource>
                                </Button>

                                <!-- Chat Button -->
                                <Button Grid.Column="3"
                                        BackgroundColor="#004f98"
                                        Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.StartChatCommand}"
                                        CommandParameter="{Binding .}"
                                        WidthRequest="36"
                                        HeightRequest="36"
                                        CornerRadius="18">
                                    <Button.ImageSource>
                                        <FileImageSource File="chat_icon.svg" />
                                    </Button.ImageSource>
                                </Button>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsWorking}"
                                   IsRunning="{Binding IsWorking}"
                                   Color="#004f98"
                                   Margin="0,32" />

            </VerticalStackLayout>
        </ScrollView>
    </Grid>
</local:FriendsListingViewBase>

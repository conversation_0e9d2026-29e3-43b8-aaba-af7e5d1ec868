﻿<?xml version="1.0" encoding="utf-8" ?>
<local:FriendsListingViewBase
    x:Class="Platform.Client.Common.Features.Friends.FriendsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:friends="clr-namespace:Platform.Client.Services.Features.Friends;assembly=Platform.Client.Services"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Friends"
    Title="Friends"
    x:DataType="local:FriendsListingViewBase"
    BackgroundColor="White"
    Shell.TitleColor="White">

    <Grid RowDefinitions="Auto,*">

        <!--  Header Section  -->
        <VerticalStackLayout
            Grid.Row="0"
            Padding="16"
            BackgroundColor="White"
            Spacing="16">

            <!--  Title and Action Buttons  -->
            <Grid ColumnDefinitions="*,Auto,Auto">
                <Label
                    Grid.Column="0"
                    FontAttributes="Bold"
                    FontSize="24"
                    Text="Friends"
                    TextColor="#004f98"
                    VerticalOptions="Center" />

                <!--  Sync Button  -->
                <Button
                    Grid.Column="1"
                    Margin="0,0,8,0"
                    BackgroundColor="#E5F3FF"
                    Command="{Binding SyncFriendsCommand}"
                    CornerRadius="20"
                    HeightRequest="40"
                    IsEnabled="{Binding IsWorking, Converter={StaticResource InverseBoolConverter}}"
                    WidthRequest="40">
                    <Button.ImageSource>
                        <FileImageSource File="{Binding IsSyncing, Converter={StaticResource BoolToStringConverter}, ConverterParameter='refresh_spin_icon.svg|refresh_icon.svg'}" />
                    </Button.ImageSource>
                </Button>

                <!--  Add Friend Button  -->
                <Button
                    Grid.Column="2"
                    BackgroundColor="#004f98"
                    Command="{Binding AddFriendCommand}"
                    CornerRadius="20"
                    HeightRequest="40"
                    WidthRequest="40">
                    <Button.ImageSource>
                        <FileImageSource File="plus_icon.svg" />
                    </Button.ImageSource>
                </Button>
            </Grid>

            <!--  Search Bar  -->
            <Border
                BackgroundColor="#F9FAFB"
                HeightRequest="44"
                Stroke="#E5E7EB"
                StrokeThickness="1">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="22" />
                </Border.StrokeShape>
                <Grid Margin="12,0" ColumnDefinitions="Auto,*,Auto">
                    <Image
                        Grid.Column="0"
                        Margin="0,0,8,0"
                        HeightRequest="16"
                        Source="search_icon.svg"
                        VerticalOptions="Center"
                        WidthRequest="16" />
                    <Entry
                        Grid.Column="1"
                        BackgroundColor="Transparent"
                        FontSize="16"
                        Placeholder="Search Friends..."
                        PlaceholderColor="#9CA3AF"
                        Text="{Binding FilterViewModel.SearchText}"
                        TextColor="#374151" />
                    <Button
                        Grid.Column="2"
                        Padding="4"
                        BackgroundColor="Transparent"
                        Command="{Binding ClearSearchCommand}"
                        HeightRequest="32"
                        IsVisible="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}}"
                        WidthRequest="32">
                        <Button.ImageSource>
                            <FileImageSource File="close_icon.svg" />
                        </Button.ImageSource>
                    </Button>
                </Grid>
            </Border>
        </VerticalStackLayout>
        <!--  Content Section  -->
        <ScrollView Grid.Row="1" BackgroundColor="White">
            <VerticalStackLayout Spacing="0">

                <!--  Success Message  -->
                <Border
                    Margin="16,0,16,8"
                    BackgroundColor="#F0FDF4"
                    IsVisible="{Binding HasSuccessMessage}"
                    Stroke="#BBF7D0"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="Auto,*">
                        <Image
                            Grid.Column="0"
                            Margin="0,2,8,0"
                            HeightRequest="20"
                            Source="check_icon.svg"
                            VerticalOptions="Start"
                            WidthRequest="20" />
                        <Label
                            Grid.Column="1"
                            FontSize="14"
                            Text="{Binding SuccessMessage}"
                            TextColor="#166534" />
                    </Grid>
                </Border>

                <!--  Error Message  -->
                <Border
                    Margin="16,0,16,8"
                    BackgroundColor="#FEF2F2"
                    IsVisible="{Binding HasError}"
                    Stroke="#FECACA"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <VerticalStackLayout Padding="12" Spacing="8">
                        <Grid ColumnDefinitions="Auto,*">
                            <Image
                                Grid.Column="0"
                                Margin="0,2,8,0"
                                HeightRequest="20"
                                Source="error_icon.svg"
                                VerticalOptions="Start"
                                WidthRequest="20" />
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Error loading friends"
                                    TextColor="#DC2626" />
                                <Label
                                    FontSize="14"
                                    Text="{Binding Error}"
                                    TextColor="#991B1B" />
                            </VerticalStackLayout>
                        </Grid>
                    </VerticalStackLayout>
                </Border>

                <!--  Search Results Info  -->
                <Border
                    Margin="16,0,16,8"
                    BackgroundColor="#E5F3FF"
                    IsVisible="{Binding HasSearchResults}"
                    Stroke="#B3D9FF"
                    StrokeThickness="1">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="8" />
                    </Border.StrokeShape>
                    <Grid Padding="12" ColumnDefinitions="*,Auto">
                        <Label
                            Grid.Column="0"
                            FontSize="14"
                            TextColor="#1E40AF">
                            <Label.FormattedText>
                                <FormattedString>
                                    <Span FontAttributes="Bold" Text="{Binding TotalRecords}" />
                                    <Span Text=" friends found for " />
                                    <Span FontAttributes="Bold" Text="{Binding FilterViewModel.SearchText, StringFormat='&quot;{0}&quot;'}" />
                                </FormattedString>
                            </Label.FormattedText>
                        </Label>
                        <Button
                            Grid.Column="1"
                            Padding="8,4"
                            BackgroundColor="Transparent"
                            Command="{Binding ClearSearchCommand}"
                            FontSize="14"
                            Text="Clear search"
                            TextColor="#1E40AF" />
                    </Grid>
                </Border>

                <!--  Empty State  -->
                <VerticalStackLayout
                    Padding="16,64,16,16"
                    HorizontalOptions="Center"
                    IsVisible="{Binding IsEmpty}"
                    Spacing="16">
                    <Image
                        HeightRequest="64"
                        HorizontalOptions="Center"
                        Source="users_icon.svg"
                        WidthRequest="64" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="18"
                        HorizontalTextAlignment="Center"
                        Text="No friends found"
                        TextColor="#111827" />
                    <Label
                        FontSize="14"
                        HorizontalTextAlignment="Center"
                        MaximumWidthRequest="320"
                        TextColor="#6B7280">
                        <Label.Text>
                            <Binding Path="FilterViewModel.SearchText">
                                <Binding.Converter>
                                    <StaticResource Key="StringToBoolConverter" />
                                </Binding.Converter>
                                <Binding.ConverterParameter>No friends match your search|You haven't added any friends yet. Start by adding your first friend!</Binding.ConverterParameter>
                            </Binding>
                        </Label.Text>
                    </Label>
                    <Button
                        Padding="24,12"
                        BackgroundColor="#004f98"
                        Command="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}, ConverterParameter='{Binding ClearSearchCommand}|{Binding AddFriendCommand}'}"
                        FontAttributes="Bold"
                        FontSize="14"
                        HeightRequest="44"
                        Text="{Binding FilterViewModel.SearchText, Converter={StaticResource StringToBoolConverter}, ConverterParameter='Clear Search|Add Your First Friend'}"
                        TextColor="White" />
                </VerticalStackLayout>

                <!--  Friends List  -->
                <CollectionView
                    BackgroundColor="Transparent"
                    IsVisible="{Binding IsEmpty, Converter={StaticResource InverseBoolConverter}}"
                    ItemsSource="{Binding Items}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate x:DataType="friends:FriendsListingViewModel">
                            <Grid
                                Padding="16,12"
                                BackgroundColor="White"
                                ColumnDefinitions="Auto,*,Auto,Auto">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.StartChatCommand}" CommandParameter="{Binding .}" />
                                </Grid.GestureRecognizers>

                                <!--  Avatar with Status  -->
                                <Grid
                                    Grid.Column="0"
                                    HeightRequest="48"
                                    WidthRequest="48">
                                    <Border
                                        BackgroundColor="#E5E7EB"
                                        HeightRequest="48"
                                        WidthRequest="48">
                                        <Border.StrokeShape>
                                            <RoundRectangle CornerRadius="24" />
                                        </Border.StrokeShape>
                                        <Grid>
                                            <Image
                                                Aspect="AspectFill"
                                                IsVisible="{Binding AvatarData, Converter={StaticResource StringToBoolConverter}}"
                                                Source="{Binding AvatarData, Converter={StaticResource Base64ToImageConverter}}" />
                                            <Label
                                                FontAttributes="Bold"
                                                FontSize="18"
                                                HorizontalOptions="Center"
                                                IsVisible="{Binding AvatarData, Converter={StaticResource InverseStringToBoolConverter}}"
                                                Text="{Binding Name, Converter={StaticResource InitialsConverter}}"
                                                TextColor="#004f98"
                                                VerticalOptions="Center" />
                                        </Grid>
                                    </Border>
                                    <!--  Online Status  -->
                                    <Ellipse
                                        Margin="0,0,-2,-2"
                                        Fill="#10B981"
                                        HeightRequest="14"
                                        HorizontalOptions="End"
                                        VerticalOptions="End"
                                        WidthRequest="14" />
                                </Grid>

                                <!--  Friend Info  -->
                                <VerticalStackLayout
                                    Grid.Column="1"
                                    Margin="12,0,8,0"
                                    Spacing="2"
                                    VerticalOptions="Center">
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="16"
                                        LineBreakMode="TailTruncation"
                                        Text="{Binding Name}"
                                        TextColor="#111827" />
                                    <Label
                                        FontSize="14"
                                        IsVisible="{Binding TagLine, Converter={StaticResource StringToBoolConverter}}"
                                        LineBreakMode="TailTruncation"
                                        Text="{Binding TagLine}"
                                        TextColor="#6B7280" />
                                </VerticalStackLayout>

                                <!--  Edit Button  -->
                                <Button
                                    Grid.Column="2"
                                    Margin="0,0,8,0"
                                    BackgroundColor="#6B7280"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.EditFriendCommand}"
                                    CommandParameter="{Binding .}"
                                    CornerRadius="18"
                                    HeightRequest="36"
                                    WidthRequest="36">
                                    <Button.ImageSource>
                                        <FileImageSource File="edit_icon.svg" />
                                    </Button.ImageSource>
                                </Button>

                                <!--  Chat Button  -->
                                <Button
                                    Grid.Column="3"
                                    BackgroundColor="#004f98"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type local:FriendsListingView}}, Path=BindingContext.StartChatCommand}"
                                    CommandParameter="{Binding .}"
                                    CornerRadius="18"
                                    HeightRequest="36"
                                    WidthRequest="36">
                                    <Button.ImageSource>
                                        <FileImageSource File="chat_icon.svg" />
                                    </Button.ImageSource>
                                </Button>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>

                <!--  Loading Indicator  -->
                <ActivityIndicator
                    Margin="0,32"
                    IsRunning="{Binding IsWorking}"
                    IsVisible="{Binding IsWorking}"
                    Color="#004f98" />

            </VerticalStackLayout>
        </ScrollView>
    </Grid>
</local:FriendsListingViewBase>

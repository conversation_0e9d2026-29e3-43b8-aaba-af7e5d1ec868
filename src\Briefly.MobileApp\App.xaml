﻿<?xml version="1.0" encoding="UTF-8" ?>
<Application
    x:Class="ModelFury.Briefly.MobileApp.App"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:converters="clr-namespace:MobileApp.MauiShared.Converters;assembly=Platform.Framework.Maui"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Add converters  -->
            <converters:InvertBooleanConverter x:Key="InvertedBoolConverter" />
            <converters:StringToBooleanConverter x:Key="StringToBooleanConverter" />
            <converters:BoolToStringConverter x:Key="BoolToStringConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>

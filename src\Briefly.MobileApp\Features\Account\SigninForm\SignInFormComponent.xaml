<?xml version="1.0" encoding="utf-8" ?>
<local:SignInFormViewBase
    x:Class="ModelFury.Briefly.MobileApp.Features.Account.SignInFormView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:ModelFury.Briefly.MobileApp.Features.Account"
    x:DataType="local:SignInFormView">
    <Grid>
        <Border BackgroundColor="#F4F4F5" StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="16,16,0,0" />
            </Border.StrokeShape>

            <ScrollView>
                <VerticalStackLayout Padding="16" Spacing="20">

                    <!--  Header Section with Icon  -->
                    <VerticalStackLayout Margin="0,20,0,10" Spacing="12">
                        <!--  Header Icon  -->
                        <Border
                            BackgroundColor="{StaticResource Gray100}"
                            HeightRequest="64"
                            HorizontalOptions="Center"
                            WidthRequest="64">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="32" />
                            </Border.StrokeShape>
                            <Image
                                HeightRequest="32"
                                HorizontalOptions="Center"
                                Source="user_group_simple_solid.svg"
                                VerticalOptions="Center"
                                WidthRequest="32" />
                        </Border>

                        <Label
                            FontAttributes="Bold"
                            FontFamily="MulishExtraBold"
                            FontSize="24"
                            HorizontalTextAlignment="Center"
                            Text="Welcome back"
                            TextColor="{StaticResource Gray600}" />
                        <Label
                            FontFamily="Poppins"
                            FontSize="14"
                            HorizontalTextAlignment="Center"
                            Opacity="0.8"
                            Text="Sign in to your account to continue"
                            TextColor="{StaticResource Secondary600}" />
                    </VerticalStackLayout>

                    <!--  Error Display  -->
                    <Border
                        BackgroundColor="#FEF2F2"
                        IsVisible="{Binding HasError}"
                        Stroke="#FECACA"
                        StrokeThickness="1">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="8" />
                        </Border.StrokeShape>
                        <Grid Padding="12" ColumnDefinitions="Auto,*">
                            <Path
                                Grid.Column="0"
                                Margin="0,2,8,0"
                                Data="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                Fill="#DC2626"
                                VerticalOptions="Start" />
                            <VerticalStackLayout Grid.Column="1" Spacing="4">
                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="Sign in failed"
                                    TextColor="#DC2626" />
                                <Label
                                    FontSize="12"
                                    Text="{Binding Error}"
                                    TextColor="#991B1B" />
                            </VerticalStackLayout>
                        </Grid>
                    </Border>

                    <!--  Sign In Form  -->
                    <VerticalStackLayout x:Name="FormContainer" Spacing="16">

                        <!--  Username Field  -->
                        <VerticalStackLayout>
                            <Label
                                FontAttributes="Bold"
                                FontFamily="MulishExtraBold"
                                FontSize="14"
                                Text="Username" />
                            <Grid>

                                <Entry
                                    BackgroundColor="Transparent"
                                    FontSize="16"
                                    Placeholder="Enter your username"
                                    PlaceholderColor="#9CA3AF"
                                    Text="{Binding SelectedItem.NickName}" />

                                <Image
                                    HorizontalOptions="End"
                                    Source="user_group_simple_solid.svg"
                                    WidthRequest="24" />
                            </Grid>
                        </VerticalStackLayout>

                        <!--  Password Field  -->
                        <VerticalStackLayout Spacing="8">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="MulishExtraBold"
                                FontSize="14"
                                Text="Password"
                                TextColor="#4E4E4E" />
                            <Grid>

                                <Entry
                                    BackgroundColor="Transparent"
                                    FontSize="16"
                                    IsPassword="{Binding SelectedItem.IsPasswordHidden, FallbackValue=True}"
                                    Placeholder="Enter your password"
                                    PlaceholderColor="#9CA3AF"
                                    Text="{Binding SelectedItem.PassKey}" />

                                <Image
                                    HorizontalOptions="End"
                                    Source="{Binding SelectedItem.ShowPassword, Converter={StaticResource BoolToStringConverter}, ConverterParameter='lock_keyhole_solid.svg|gear_light.svg'}"
                                    WidthRequest="24">
                                    <Image.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding TogglePasswordVisibilityCommand}" />
                                    </Image.GestureRecognizers>
                                </Image>

                            </Grid>
                        </VerticalStackLayout>

                        <!--  Enhanced Sign In Button  -->
                        <Button Command="{Binding SaveCommand}" TextColor="White">
                            <Button.ImageSource>
                                <FileImageSource File="lock_icon.svg" />
                            </Button.ImageSource>
                            <Button.Text>
                                <Binding Path="IsWorking">
                                    <Binding.Converter>
                                        <StaticResource Key="BoolToStringConverter" />
                                    </Binding.Converter>
                                    <Binding.ConverterParameter>Signing in...|Sign in</Binding.ConverterParameter>
                                </Binding>
                            </Button.Text>
                        </Button>



                        <!--  Sign Up Link  -->
                        <HorizontalStackLayout
                            Margin="0,20,0,0"
                            HorizontalOptions="Center"
                            Spacing="5">
                            <Label
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Don't have an account?"
                                TextColor="#6B7280" />
                            <Label
                                FontAttributes="Bold"
                                FontFamily="Poppins"
                                FontSize="14"
                                Text="Sign up here"
                                TextColor="#004f98">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding GoToSignupCommand}" />
                                </Label.GestureRecognizers>
                            </Label>
                        </HorizontalStackLayout>
                    </VerticalStackLayout>
                </VerticalStackLayout>
            </ScrollView>
        </Border>
        <Grid BackgroundColor="#********" IsVisible="{Binding IsWorking}">

            <Border
                Padding="30"
                BackgroundColor="White"
                HorizontalOptions="Center"
                VerticalOptions="Center">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="16" />
                </Border.StrokeShape>
                <Border.Shadow>
                    <Shadow
                        Brush="Gray"
                        Opacity="0.2"
                        Radius="10"
                        Offset="0,4" />
                </Border.Shadow>
                <VerticalStackLayout HorizontalOptions="Center" Spacing="15">
                    <ActivityIndicator IsRunning="True" Color="{StaticResource Secondary700}" />
                    <Label
                        FontAttributes="Bold"
                        FontSize="12"
                        HorizontalTextAlignment="Center"
                        Text="Working, please wait...!"
                        TextColor="Black" />
                </VerticalStackLayout>
            </Border>
        </Grid>
    </Grid>
</local:SignInFormViewBase>
﻿using System.Globalization;

namespace MobileApp.MauiShared.Converters
{
    public class StringToBooleanConverter : IValueConverter
    {
         public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null)
                return false;
            return true;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class BoolToStringConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? parameter?.ToString().Split('|').First(): parameter?.ToString().Split('|').Last();
            }
            return string.Empty; // Default case if not a boolean
        }
        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string strValue)
            {
                return strValue.Equals("True", StringComparison.OrdinalIgnoreCase);
            }
            return false; // Default case if not a string
        }
    }
}
